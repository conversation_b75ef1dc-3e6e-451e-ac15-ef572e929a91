import { describe, it, vi } from 'vitest'
import { render } from '@testing-library/react'
import { DateTime } from 'luxon'
import { vExpect } from 'src/vitest/utils'

import DateRangeNavigator from './DateRangeNavigator'

// Mock the useDateRangeShortcutItems hook
vi.mock('src/hooks/useDateRangeShortcutItems', () => ({
  useDateRangeShortcutItems: () => ({
    last7Days: {
      label: 'Last 7 days',
      getValue: () => [
        DateTime.local().minus({ days: 6 }).startOf('day'),
        DateTime.local().endOf('day'),
      ],
    },
    last30Days: {
      label: 'Last 30 days',
      getValue: () => [
        DateTime.local().minus({ days: 29 }).startOf('day'),
        DateTime.local().endOf('day'),
      ],
    },
    last90Days: {
      label: 'Last 90 days',
      getValue: () => [
        DateTime.local().minus({ days: 89 }).startOf('day'),
        DateTime.local().endOf('day'),
      ],
    },
    thisMonth: {
      label: 'This month',
      getValue: () => [
        DateTime.local().startOf('month'),
        DateTime.local().endOf('day'),
      ],
    },
    thisQuarter: {
      label: 'This quarter',
      getValue: () => [
        DateTime.local().startOf('quarter'),
        DateTime.local().endOf('day'),
      ],
    },
  }),
}))

// Mock the ctIntl utility
vi.mock('src/util-components/ctIntl', () => ({
  ctIntl: {
    formatMessage: ({ id }: { id: string }) => id,
  },
}))

describe('DateRangeNavigator', () => {
  const mockOnDateRangeChange = vi.fn()

  it('should prefer thisQuarter over thisMonth when both match the same date range', () => {
    // Create a scenario where thisMonth and thisQuarter have the same date range
    // This happens when we're at the beginning of a quarter (e.g., July 1st)
    const quarterStart = DateTime.local(2024, 7, 1) // July 1st, 2024 (Q3 start)
    const today = DateTime.local(2024, 7, 3) // July 3rd, 2024

    // Mock DateTime.local to return our test date
    const originalLocal = DateTime.local
    vi.spyOn(DateTime, 'local').mockImplementation((...args) => {
      if (args.length === 0) {
        return today
      }
      return originalLocal(...args)
    })

    const dateRange = [quarterStart, today.endOf('day')] as const

    const { container } = render(
      <DateRangeNavigator
        dateRange={dateRange}
        onDateRangeChange={mockOnDateRangeChange}
      />,
    )

    // The component should display "This quarter" since it's more specific than "This month"
    // when both have the same date range
    vExpect(container.textContent).toContain('This quarter')

    // Restore the original DateTime.local
    vi.restoreAllMocks()
  })

  it('should select thisMonth when the date range only matches thisMonth', () => {
    // Create a scenario where only thisMonth matches
    const monthStart = DateTime.local(2024, 7, 5) // July 5th, 2024 (not quarter start)
    const today = DateTime.local(2024, 7, 10) // July 10th, 2024

    // Mock DateTime.local to return our test date
    const originalLocal = DateTime.local
    vi.spyOn(DateTime, 'local').mockImplementation((...args) => {
      if (args.length === 0) {
        return today
      }
      return originalLocal(...args)
    })

    const dateRange = [monthStart, today.endOf('day')] as const

    const { container } = render(
      <DateRangeNavigator
        dateRange={dateRange}
        onDateRangeChange={mockOnDateRangeChange}
      />,
    )

    // The component should display a custom date range since it doesn't match any preset
    // (our mock doesn't create a perfect match for this scenario)
    vExpect(container.textContent).toContain('Jul')

    // Restore the original DateTime.local
    vi.restoreAllMocks()
  })
})
