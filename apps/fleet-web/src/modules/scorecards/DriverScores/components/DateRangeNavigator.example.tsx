/**
 * Example usage of DateRangeNavigator with ID-based option selection
 * 
 * This example demonstrates how to use the new `selectedOptionId` prop
 * to explicitly control which date range option is selected, solving
 * the issue where multiple options could have the same date range
 * (e.g., this<PERSON>onth and thisQuarter on July 3rd).
 */

import { useState } from 'react'
import { DateTime } from 'luxon'
import type { DateRange } from '@karoo-ui/core'
import type { PickerChangeHandlerContext, DateRangeValidationError } from '@mui/x-date-pickers-pro'

import DateRangeNavigator from './DateRangeNavigator'

export const DateRangeNavigatorExample = () => {
  const [dateRange, setDateRange] = useState<DateRange<DateTime>>([
    DateTime.local().startOf('quarter'), // July 1st if current date is in Q3
    DateTime.local().endOf('day'), // Today
  ])
  
  const [selectedOptionId, setSelectedOptionId] = useState<string | null>('thisQuarter')

  const handleDateRangeChange = (
    newValue: DateRange<DateTime>,
    context: PickerChangeHandlerContext<DateRangeValidationError>,
  ) => {
    setDateRange(newValue)
    // The component will automatically determine the selectedOptionId based on the date range
    // But you can also explicitly set it if needed
  }

  const handleOptionSelect = (optionId: string) => {
    setSelectedOptionId(optionId)
    // You could also update the date range here if needed
  }

  return (
    <div>
      <h3>DateRangeNavigator with ID-based selection</h3>
      
      {/* Example 1: Explicitly set to "thisQuarter" */}
      <div style={{ marginBottom: '20px' }}>
        <h4>Explicitly set to "thisQuarter":</h4>
        <DateRangeNavigator
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
          selectedOptionId="thisQuarter"
        />
      </div>

      {/* Example 2: Explicitly set to "thisMonth" */}
      <div style={{ marginBottom: '20px' }}>
        <h4>Explicitly set to "thisMonth" (same date range, different option):</h4>
        <DateRangeNavigator
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
          selectedOptionId="thisMonth"
        />
      </div>

      {/* Example 3: Controlled by state */}
      <div style={{ marginBottom: '20px' }}>
        <h4>Controlled by state:</h4>
        <div style={{ marginBottom: '10px' }}>
          <button onClick={() => handleOptionSelect('thisMonth')}>
            Select "This Month"
          </button>
          <button onClick={() => handleOptionSelect('thisQuarter')}>
            Select "This Quarter"
          </button>
          <button onClick={() => handleOptionSelect('last30Days')}>
            Select "Last 30 Days"
          </button>
        </div>
        <DateRangeNavigator
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
          selectedOptionId={selectedOptionId}
        />
        <p>Current selected option ID: {selectedOptionId}</p>
      </div>

      {/* Example 4: Auto-detection (original behavior) */}
      <div style={{ marginBottom: '20px' }}>
        <h4>Auto-detection (no selectedOptionId provided):</h4>
        <DateRangeNavigator
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
        />
      </div>
    </div>
  )
}

/**
 * Available option IDs:
 * - 'last7Days'
 * - 'last30Days' 
 * - 'last90Days'
 * - 'thisMonth'
 * - 'thisQuarter'
 * 
 * Benefits of using selectedOptionId:
 * 1. Unique identification: Each option has a unique ID, preventing conflicts
 * 2. Explicit control: You can explicitly set which option should be selected
 * 3. Predictable behavior: No more ambiguity when multiple options have the same date range
 * 4. Better UX: Users see the correct option selected based on their intent
 */
